import i18n from 'i18next'
import LngDetector from 'i18next-browser-languagedetector'
import ChainedBackend from 'i18next-chained-backend'
import LocalStorageBackend from 'i18next-localstorage-backend'
import Locize from 'i18next-locize-backend'
import { locizeEditorPlugin, locizePlugin } from 'locize'
import LastUsed from 'locize-lastused'
import { initReactI18next } from 'react-i18next'
import { ASSET } from '../common/constants/Assets'
import { envs } from './envs'
import { isLite } from './lite'

const { environment } = envs

/**
 * Language configuration object interface
 */
interface LanguageConfig {
  /** Language code in BCP 47 format (e.g., 'en-US', 'pt-BR') */
  readonly value: string
  /** Short language name for UI display (e.g., 'EN', 'PT') */
  readonly name: string
  /** Flag icon asset for the language */
  readonly icon: string
  /** Full language name for accessibility and detailed display */
  readonly fullName: string
}

/**
 * Locize backend configuration interface
 */
interface LocizeConfig {
  /** Locize project identifier - required for Locize backend */
  readonly projectId: string
  /** Locize API key for backend communication */
  readonly apiKey: string | undefined
  /** Version of translations to use */
  readonly version: 'latest'
  /** Reference language for fallback translations */
  readonly referenceLng: string
}

/**
 * Local storage cache configuration interface
 */
interface LocalStorageCacheConfig {
  /** Cache expiration time in milliseconds */
  readonly expirationTime: number
}

/**
 * Supported languages configuration array
 * Contains language settings for English (US), Portuguese (Brazil), and Spanish (El Salvador)
 */
export const languages: LanguageConfig[] = [
  { value: 'en-US', name: 'EN', icon: ASSET.flagUS, fullName: 'English' },
  { value: 'pt-BR', name: 'PT', icon: ASSET.flagPT, fullName: 'Português' },
  { value: 'es-SV', name: 'ES', icon: ASSET.flagES, fullName: 'Español' },
]

/**
 * Locize backend configuration for remote translation management
 */
const locizeConfig: LocizeConfig = {
  projectId: import.meta.env.VITE_LOCIZE_PROJECT_ID ?? '',
  apiKey: import.meta.env.VITE_LOCIZE_API_KEY,
  version: 'latest',
  referenceLng: 'en-US',
} as const

/**
 * Local storage cache configuration for translation caching
 * Cache duration varies by environment: 1 hour in production, 24 hours in development
 */
const localStorageCacheConfig: LocalStorageCacheConfig = {
  // Expiration time in ms
  expirationTime:
    // Hold cache for 1 hour in production
    environment === 'production' ? 60 * 60 * 1_000 : 24 * 60 * 60 * 1_000,
} as const

// Configure environment-specific plugins
if (environment === 'production') {
  // Pings locize every 3 minutes to communicate if there are keys
  // that are not being used in the app
  i18n.use(LastUsed)
}

// Locize editor only available in staging
if (environment !== 'production') {
  i18n
    .use(locizePlugin)
    // the default "incontext=true" query param does not work
    // it bugs out the editor and does not fetch the translations!!!
    .use(locizeEditorPlugin({ show: false, qsProp: 'edit' }))
}

// Initialize i18n with configuration
i18n
  .use(LngDetector)
  .use(initReactI18next)
  .use(ChainedBackend)
  .init({
    // Language validation
    supportedLngs: languages.map((lng: LanguageConfig): string => lng.value),
    fallbackLng: languages[0].value,
    // Detection config
    detection: {
      order: [
        'localStorage',
        'querystring',
        'navigator',
        'cookie',
        'sessionStorage',
        'htmlTag',
        'path',
        'subdomain',
      ],
      caches: ['localStorage', 'sessionStorage', 'cookie'],
    },

    // Backend config
    backend: {
      backends: [LocalStorageBackend, Locize],
      backendOptions: [localStorageCacheConfig, locizeConfig],
    },
    locizeLastUsed: { ...locizeConfig },
    // Other config
    ns: isLite ? 'lite' : 'default',
    react: {
      bindI18n: 'languageChanged editorSaved',
    },
    interpolation: {
      escapeValue: false,
    },
  })

/**
 * Configured i18next instance for internationalization
 * Supports English (US), Portuguese (Brazil), and Spanish (El Salvador)
 * Uses Locize for remote translation management and local storage for caching
 */
export default i18n
